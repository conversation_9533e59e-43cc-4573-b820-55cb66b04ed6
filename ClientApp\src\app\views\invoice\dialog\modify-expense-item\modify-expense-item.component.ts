import { ModifyTaxesDialog } from './../../../../service/dialog/modify-taxes.dialog';
import { DecimalPipe } from './../../../../pipes/decimal.pipe';
import { Component, inject, Inject } from '@angular/core';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { SharedModule } from 'app/module/shared.module';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { calculateTotalInvoiceItem, getNameTaxes } from 'app/utils/invoice.helper';

@Component({
  selector: 'app-modify-expense-item',
  templateUrl: './modify-expense-item.component.html',
  styleUrl: './modify-expense-item.component.scss',
  standalone: true,
  imports: [
    SharedModule,
    InnoModalWrapperComponent,
    InnoModalFooterComponent,
    InnoFormInputComponent,
    FormatNumberPipe,
    DecimalPipe
  ]
})
export class ModifyExpenseItemComponent {
  public invoiceItemForm!: UntypedFormGroup;
  today = new Date();
  public total?: number
  public listTaxName?: string = ''
  rate: number;
  quantity: number;

  private formBuilder = inject(UntypedFormBuilder)
  static getComponent(): typeof ModifyExpenseItemComponent {
    return ModifyExpenseItemComponent;
  }
  constructor(
    public dialogRef: MatDialogRef<ModifyExpenseItemComponent>,
    private modifyTaxesDialog: ModifyTaxesDialog,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    const { description = '', rate, qty, taxes = [] } = data || {}
    this.rate = rate || 0
    this.quantity = qty || 1
    this.invoiceItemForm = this.formBuilder.group({
      description: [description, []],
      rate: [rate || 0, Validators.compose([Validators.required])],
      qty: [qty || 1, Validators.compose([Validators.required])],
      taxes: [taxes || []],
    });

    // Initialize tax name display - handle empty taxes array
    if (taxes && taxes.length > 0) {
      this.listTaxName = getNameTaxes(taxes)
    } else {
      this.listTaxName = ''
    }

    this.total = calculateTotalInvoiceItem(this.rate, this.quantity);

    this.invoiceItemForm.valueChanges.subscribe((values) => {
      this.total = calculateTotalInvoiceItem(values.rate, values.qty);
      this.listTaxName = getNameTaxes(values.taxes || [])
    });
  }
  onChangeTax($event, index: number) {
    // console.log("onChangeTax", $event, index);

  }
  calculateTotalTax(tax: number) {
    if (!tax || !this.rate || !this.quantity) return 0;
    return Number(((this.rate * this.quantity) * (tax / 100)).toFixed(2));
  }


  get f() {
    return this.invoiceItemForm.controls as Record<string, FormControl>;;
  }
  handleClose() {
    this.dialogRef.close();
  }
  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }

  handleCancel() {
    this.dialogRef.close();
  }

  handleSubmit() {
    if (this.invoiceItemForm.invalid) {
      this.markAllControlsAsTouched();
      return;
    }

    const payload: Record<string, any> = {
      description: this.f['description'].value,
      rate: this.f['rate'].value,
      qty: this.f['qty'].value,
      total: this.total ?? 0,
      taxes: this.f['taxes'].value,
      date: this.today,
      dateSelectItem: this.today
    }
    if (this.data?.id) {
      payload["id"] = this.data.id
    }
    this.dialogRef.close(payload)
  }

  handleModifyTaxes() {
    // Prepare tax data for the dialog - handle both companyTax and regular tax structures
    const currentTaxes = this.f["taxes"]?.value ?? [];
    let taxes = [];

    if (currentTaxes.length > 0) {
      currentTaxes.forEach(itemtax => {
        if (itemtax.companyTax) {
          itemtax.companyTax.selected = true;
          taxes.push(itemtax.companyTax)
        }
        else {
          taxes.push(itemtax)
        }
      });
    }

    const dialogRef = this.modifyTaxesDialog.open(taxes.filter(x => x.selected));

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res?.taxes) return
        this.f["taxes"].setValue(res.taxes)
      })
    });
  }
}
