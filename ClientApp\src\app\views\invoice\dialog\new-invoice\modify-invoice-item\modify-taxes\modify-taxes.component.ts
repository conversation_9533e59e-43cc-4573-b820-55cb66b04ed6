import { StoreService } from 'app/service/store.service';
import { InputTaxComponent } from './input-tax/input-tax.component';
import { Component, DestroyRef, EventEmitter, Inject, inject, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { AddTax } from 'app/dto/interface/addTax.interface';
import { TaxService } from 'app/service/tax.service';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
import { CompanyTaxService } from 'app/service/company-tax.service';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CompanyTax } from 'app/dto/interface/CompanyTax.interface';
import { ToastService } from 'app/service/toast.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-modify-taxes',
  standalone: true,
  imports: [
    SharedModule,
    InputTaxComponent,
    InnoModalWrapperComponent,
    InnoModalFooterComponent,
    InnoFormCheckboxComponent
  ],
  templateUrl: './modify-taxes.component.html',
  styleUrl: './modify-taxes.component.scss'
})
export class ModifyTaxesComponent implements OnInit, OnDestroy {
  private listIndexTaxesSelected: Number[] = []
  @Output() onstop = new EventEmitter<any>();
  @Output() emitTax = new EventEmitter<any>();
  @Output() oncancel = new EventEmitter<any>();
  @Input() ApplyAll: boolean = false
  listCompanyTax: CompanyTax[] = []
  selectAll: boolean = false
  public listTax: AddTax[] = []
  private originData = ""

  public toastService = inject(ToastService)
  private companyTaxService = inject(CompanyTaxService)
  public _storeService = inject(StoreService)
  public translate = inject(TranslateService)
  destroyRef = inject(DestroyRef);

  static getComponent(): typeof ModifyTaxesComponent {
    return ModifyTaxesComponent;
  }
  constructor(
    public dialogRef: MatDialogRef<ModifyTaxesComponent>, @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.listTax = this.data ?? []
  }
  handleClose() {
    this.dialogRef.close();
  }
  ngOnDestroy(): void {
    this.taxes = [];
  }
  ngOnInit(): void {
    this.GetAllCompanyTax();
  }

  GetAllCompanyTax() {
    let payload: Parameter = {
      Page: 1,
      PageSize: 50,
      Search: "",

    }
    this.companyTaxService.GetAllCompanyTax(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        this.originData = JSON.stringify(res.data);
        this.listCompanyTax = res.data;
        this.taxes = res.data.slice()
        if (this.taxes.length > 0 && this.listTax.length > 0) {
          this.taxes.forEach((item: any) => {
            let found = this.listTax.some((update: any) => {
              if (update.companyTaxId) {
                return update.companyTaxId === item.id;
              }
              if (update.companyTax) {
                return update.companyTax.id === item.id;
              }
              return update.id === item.id || update.name === item.name;
            });
            item.selected = found;
          });
        }

        // Ensure maximum 2 taxes are selected
        const selectedTaxes = this.taxes.filter((tax: any) => tax.selected);
        if (selectedTaxes.length > 2) {
          // Keep only the first 2 selected taxes
          let selectedCount = 0;
          this.taxes.forEach((tax: any) => {
            if (tax.selected && selectedCount >= 2) {
              tax.selected = false;
            } else if (tax.selected) {
              selectedCount++;
            }
          });
        }

        this.listIndexTaxesSelected = this.taxes
          .map((item, index) => item.selected ? index : -1)
          .filter(index => index !== -1);
      }
    });
  }

  stopPropagation(event: any) {
    this.onstop.emit(event)
  }
  taxes = [
    { selected: false, taxeNumber: "", name: '', amount: 0 },
  ];

  addTax() {
    this.taxes.push({ selected: false, taxeNumber: "", name: '', amount: 0 });
  }

  Delete(index: number) {
    this.taxes.splice(index, 1)
    const indexExist = this.listIndexTaxesSelected.indexOf(index)
    if (indexExist !== -1) {
      this.listIndexTaxesSelected.splice(indexExist, 1)
    }
  }

  handleCancel() {
    this.oncancel.emit(true)
    this.dialogRef.close();
  }


  handleSubmit() {
    this._storeService.set_ApplyTaxAll(this.selectAll)
    // let allTaxes = [...(this.listTax ?? []), ...this.taxes]
    this.taxes = this.taxes.filter(x => x.name || x.taxeNumber || x.amount)
    const nameCounts = this.taxes.reduce((acc, item) => {
      acc[item.name] = (acc[item.name] || 0) + 1;
      return acc;
    }, {});
    const duplicateNames = Object.keys(nameCounts).filter(name => nameCounts[name] > 1);
    if (duplicateNames.length > 0) {
      this.toastService.showWarning(this.translate.instant("TOAST.Warning"), `${this.translate.instant("TOAST.Tax")} ( ${duplicateNames.toString()})`)
      return;
    }
    this.dialogRef.close({ taxes: this.taxes.filter(t => t.selected) });
    //  save tax for company
    if (JSON.stringify(this.taxes) !== JSON.stringify(this.originData)) {
      let payload = {
        listItem: this.taxes
      }
      this.companyTaxService.Create(payload).subscribe()

    }


  }
  handleSelectedTax(value: boolean, index: number) {
    const indexExist = this.listIndexTaxesSelected.indexOf(index);
    if (indexExist !== -1) {
      // Deselecting a tax
      this.taxes[index].selected = false;
      this.listIndexTaxesSelected.splice(indexExist, 1);
    } else {
      // Selecting a tax - limit to maximum 2 taxes
      if (this.listIndexTaxesSelected.length === 2) {
        const indexUnselected = this.listIndexTaxesSelected.shift() as number;
        this.taxes[indexUnselected].selected = false;
      }
      this.listIndexTaxesSelected.push(index);
      this.taxes[index].selected = true;
    }

    // Update all tax selection states to match the selected indices
    this.taxes.forEach((item: any, idx: number) => {
      item.selected = this.listIndexTaxesSelected.includes(idx);
    });
  }

  handleChangeCheckAll(value: any) {
    this.selectAll = value
  }
}
