import { CompanyService } from 'app/service/company.service';
import { CdnService } from './../../../../service/cdn.service';
import { User } from 'app/dto/interface/user.interface';
import { DecimalPipe } from './../../../../pipes/decimal.pipe';
import { SpinnerService } from 'app/service/spinner.service';
import { AddNewItemDialog } from 'app/service/dialog/add-new-item-invoice.dialog';
import { SendEstimateDialog } from './../../../../service/dialog/send-estimate.dialog';
import { InvoiceService } from './../../../../service/invoice.service';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, Inject, inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SharedModule } from 'app/module/shared.module';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ToastService } from 'app/service/toast.service';
import moment from 'moment-timezone';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoFormSelectSearchComponent } from 'app/component/inno-form-select-search/inno-form-select-search.component';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoFormDatepickerComponent } from 'app/component/inno-form-datepicker/inno-form-datepicker.component';
import { InnoFormTextareaComponent } from 'app/component/inno-form-textarea/inno-form-textarea.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';

import { DropdownOptionsService } from 'app/service/dropdown-options.service';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { AvatarModule } from 'ngx-avatars';
import { InnoUploadComponent } from 'app/component/inno-upload/inno-upload.component';
import { getBase64AndFileName, getFullAddress } from 'app/helpers/common.helper';
import { Router, RouterModule } from '@angular/router';
import { DataService } from 'app/service/data.service';
import { Invoice } from 'app/dto/interface/invoice.interface';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { InvoiceItem } from '../../../../dto/interface/invoiceItem.interface';
import { ModifyInvoiceItemDialog } from '../../../../service/dialog/modify-invoice-item.dialog';
import { ModifyTaxesDialog } from '../../../../service/dialog/modify-taxes.dialog';
import { SelectTimeTrackingDialog } from '../../../../service/dialog/select-time-tracking.dialog';
import {
  CdkDragDrop,
  CdkDrag,
  CdkDropList,
  CdkDropListGroup,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import { DatePipe } from '@angular/common';
import { calculateGroupedTaxes, calculateTotalInvoiceItem, getNameTaxes } from 'app/utils/invoice.helper';
interface IModifyInvoice {
  id?: string;
  clientId?: string;
  invoiceDate?: Date,
  invoiceNumber?: string,
  projectId: string,
  // dueDate?: Date,
  notes?: string,
  img?: string,
  position?: number,
  project: any,
  createdAt: any,
  company: any;
  itemInvoices: InvoiceItem[]
}
@Component({
  selector: 'app-new-estimate',
  standalone: true,
  providers: [LayoutUtilsService],
  imports: [
    SharedModule,
    AvatarModule,
    RouterModule,
    InnoModalWrapperComponent,
    InnoFormSelectSearchComponent,
    InnoFormInputComponent,
    InnoFormDatepickerComponent,
    InnoFormTextareaComponent,
    InnoTableActionComponent,
    InnoModalFooterComponent,
    InnoUploadComponent,
    FormatNumberPipe,
    DecimalPipe,
    CdkDropListGroup,
    CdkDropList,
    CdkDrag
  ],
  templateUrl: './new-estimate.component.html',
  styleUrl: './new-estimate.component.scss'

})
export class NewEstimateComponent implements OnInit {
  imageUrl!: string;
  projectName!: string;
  projectId!: string;
  inforUser: User
  InforCompany: any
  public invoiceForm!: UntypedFormGroup;
  public itemInvoice2 = [];
  public calculateTotalInvoiceItem = calculateTotalInvoiceItem
  public getNameSelectedTaxes = getNameTaxes
  public calculateGroupedTaxes = calculateGroupedTaxes
  public projectAndClientOptions: IFilterDropdownOption[] = []
  public subtotal: number = 0
  public totalAmount: number = 0
  public selectedDateStart = this.formatDate(new Date())
  public selectedDateEnd = this.formatDate(new Date());
  taxArray: { name: string, total: number, numberTax: string, amount: number }[] = [];
  private base64!: string;
  private type!: string;
  private filename!: string;
  private payment: any[] = []
  public sumtax: number = 0;
  private invoiceNumber: string = "0000001"
  private listTax: any[] = []

  private formBuilder = inject(UntypedFormBuilder)
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);
  public _toastService = inject(ToastService)
  public _storeService = inject(StoreService)
  private _invoiceService = inject(InvoiceService)
  private dataService = inject(DataService)
  private _spinnerService = inject(SpinnerService)
  private dropdownOptionService = inject(DropdownOptionsService)
  private layoutUtilsService = inject(LayoutUtilsService)
  private addNewItemDialog = inject(AddNewItemDialog)
  private cdnService = inject(CdnService)
  private _companyServices = inject(CompanyService)
  @ViewChild('selectSearchClientElement') selectSearchClientElement!: InnoFormSelectSearchComponent;

  static getComponent(): typeof NewEstimateComponent {
    return NewEstimateComponent;
  }

  constructor(public dialogRef: MatDialogRef<NewEstimateComponent>,
    private modifyInvoiceItemDialog: ModifyInvoiceItemDialog,
    private modifyTaxesDialog: ModifyTaxesDialog,
    private selectTimeTrackingDialog: SelectTimeTrackingDialog,
    private sendEstimateDialog: SendEstimateDialog,
    @Inject(MAT_DIALOG_DATA) public data?: IModifyInvoice) {
    this.invoiceForm = this.formBuilder.group({
      clientId: [data?.clientId ?? "", Validators.compose([Validators.required])],
      invoiceDate: [data?.invoiceDate ?? null, Validators.compose([Validators.required])],
      // dueDate: [data?.dueDate ?? null, Validators.compose([Validators.required])],
      invoiceNumber: [data?.invoiceNumber],
      projectId: [""],
      notes: [data?.notes ?? ""],
      itemInvoice: [[]],
    });
    this.invoiceForm.get('itemInvoice')?.valueChanges.subscribe(listInvoice => {
      this.subtotal = 0
      listInvoice?.forEach((invoiceItem: any) => {
        const totalInvoiceItem = calculateTotalInvoiceItem(invoiceItem?.rate, invoiceItem?.qty);
        this.subtotal = this.subtotal + totalInvoiceItem
      })

      // Calculate with discount if exist
      this.calculateAllTax();
    });

    this.invoiceForm.get('invoiceNumber')?.disable();
    this.inforUser = this._storeService.get_InforUser();
    if (this.data) {
      // Set taxes as selected
      this.data.itemInvoices.forEach(c => {
        c.taxes.forEach(t => {
          t.selected = true
        })
      });
      this.projectId = this.data?.projectId;
      this.projectName = this.data?.project?.projectName;
      this.f['itemInvoice'].setValue((this.data?.itemInvoices ?? []))
      if (this.data?.img) {
        this.GetImg(this.data?.img);
      }
    }
  }

  async handleSelectProject(item: IFilterDropdownOption) {

    let newClientId = ""
    let newProjectID = ""

    if (item.metadata?.type == 'client') {
      newClientId = item.value
      newProjectID = ""
    } else {
      this.projectName = item.label;
      newClientId = item.metadata?.objectClient?.id
      newProjectID = item.value
    }

    const currentClientId = this.f['clientId'].value
    const isSameClient = currentClientId === newClientId
    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    if (!isSameClient && currentPaymentInvoice.length) {
      const isConfirm = await this.layoutUtilsService.alertConfirm({
        title: 'Warning',
        description: 'You are changing the client, and the invoices will be reset. Are you sure you want to continue?',
      })
      if (!isConfirm) return;
      this.f['itemInvoice'].setValue([])
    }

    this.f['clientId'].setValue(newClientId)
    this.f['projectId'].setValue(newProjectID)
    this.selectSearchClientElement.handleCloseSearchResult()
  }

  ngOnInit(): void {
    this.dropdownOptionService
      .getDropdownOptionsProjectAndClient()
      .then((projectAndClientOptions) => this.projectAndClientOptions = projectAndClientOptions)
    if (!this.data) {
      this.CountEstimate();
    }
    this.GetInforCompany();
  }
  GetInforCompany() {
    this._companyServices.GetInforCompany().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.InforCompany = res;
      }
    })
  }
  get businessInfo() {
    const business = this.InforCompany
    return {
      businessName: business?.businessName ?? '',
      businessPhoneNumber: business?.phone ?? '',
      businessAddress: getFullAddress({
        addressLine1: business?.adress ?? '',
        addressLine2: business?.adress2 ?? '',
        stateProvince: business?.province ?? '',
        postalCode: business?.postalCode ?? '',
        country: business?.country ?? '',
      }),
    }
  }

  get f() {
    return this.invoiceForm.controls as Record<string, FormControl>;;
  }

  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  convertToHours(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours + minutes / 60;
  }

  CountEstimate() {
    this._invoiceService.CountEstimate().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {

        if (res == 0) {
          const number = 1;
          this.invoiceNumber = number.toString().padStart(7, '0');
          this.f['invoiceNumber'].setValue(this.invoiceNumber)
          return;
        }
        const number = res + 1;
        this.invoiceNumber = number.toString().padStart(7, '0');
        this.f['invoiceNumber'].setValue(this.invoiceNumber)
      }
      else {
        const number = 1;
        this.invoiceNumber = number.toString().padStart(7, '0');
        this.f['invoiceNumber'].setValue(this.invoiceNumber)
        return;

      }
    }
    )
  }

  calculateAllTax() {
    this.taxArray = [];
    this.sumtax = 0;

    const resultTax = calculateGroupedTaxes(this.f['itemInvoice'].value)
    this.taxArray = Object.values(resultTax.totalTaxes);
    this.sumtax = resultTax.grandTotalTax
    this.totalAmount = this.subtotal + this.CheckIsNaN(this.sumtax)
  }


  handleDeleteInvoiceItem(index: number) {
    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    if (!currentPaymentInvoice?.length) return

    currentPaymentInvoice.splice(index, 1)
    this.f['itemInvoice'].setValue(currentPaymentInvoice)
  }

  handleCancel() {
    this.dialogRef.close();
  }

  get getInvoicePayload() {
    if (this.invoiceForm.invalid) return null;

    let invoice: Invoice = {
      clientId: this.f['clientId'].value,
      invoiceNumber: this.f['invoiceNumber'].value,
      invoiceDate: moment.utc(this.f['invoiceDate'].value).toDate(),
      // dueDate: moment.utc(this.f['dueDate'].value).toDate(),
      reference: '',
      notes: this.f['notes'].value,
      projectId: this.f['projectId'].value,
      payments: this.payment,
      isEstimate: true,
      taxes: this.listTax,
      base64: this.base64,
      type: this.type,
      filename: this.filename,
      itemInvoices: this.f['itemInvoice'].value.map(item => ({
        ...item,
        taxes: item.taxes.some(tax => tax.companyTax)
          ? item.taxes.map(({ companyTax, ...rest }) => rest)
          : item.taxes.filter(tax => tax.selected)
      })),
      paidAmount: this.subtotal,
      taxAmount: this.sumtax,
      totalAmount: this.totalAmount,
      rate: 0,
      status: 0,
      timeAmount: 0,
    };
    if (this.data) {
      invoice["id"] = this.data?.id
      invoice["createdAt"] = this.data?.createdAt
    }
    return invoice;
  }

  handleAddUnBillTime() {
    const clientId = this.f['clientId'].value
    if (!clientId) {
      this._toastService.showWarning("No selected client", "Please select a client to add the time.")
      return
    }

    const client = this.projectAndClientOptions.find(item => item.value === clientId)?.metadata?.client
    if (!client) return this._toastService.showWarning("Not fount client")


    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    const listIdTimeTrackingSelected = currentPaymentInvoice.map(item => item.trackingId).filter(x => x)
    const payload = {
      client,
      listIdTimeTrackingSelected
    }



    const dialogRef = this.selectTimeTrackingDialog.open(payload);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((_listTimeTrackingSelected) => {
        if (!_listTimeTrackingSelected?.length) return
        currentPaymentInvoice.push(..._listTimeTrackingSelected.map(({ user, ...rest }) => rest))
        this.f['itemInvoice'].setValue(currentPaymentInvoice)
      })
    });
  }

  handleSave() {
    if (this.invoiceForm.invalid) {
      this.markAllControlsAsTouched();
      return
    }
    this._spinnerService.show();
    if (this.data) {
      this._invoiceService.UpdateInvoice(this.getInvoicePayload)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe(res => {
          if (res) {
            this._spinnerService.hide()
            this.dataService.triggerRefreshInvoice()
            this.dialogRef.close(res)
            this._toastService.showSuccess("Save", "Success");
          }
        })
    }
    else {
      this._invoiceService.CreatedInvoice(this.getInvoicePayload)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe(res => {
          if (res) {
            this._spinnerService.hide();
            this.dataService.triggerRefreshInvoice()
            this.dialogRef.close()
            this._toastService.showSuccess("Save", "Success");
          }
        })
    }

  }
  handleAddNewItem() {
    const dialogRef = this.addNewItemDialog.open(this.getInvoicePayload);
    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    dialogRef.then((c) => {
      c.afterClosed().subscribe((itemnew) => {
        if (!itemnew?.length) return
        const updatedData = itemnew.map(({ id, isServices, taxes, itemName, projectId, serviceId, serviceName, projectName, description, createdAt, ...rest }) => ({
          ...rest,
          description: description,
          itemName: itemName,
          itemId: isServices == false ? id : null,
          projectId: isServices == true ? projectId : null,
          projectName: projectName ?? null,
          serviceId: isServices == true ? id : null,
          serviceName: serviceName,
          date: createdAt ?? new Date(),
          dateSelectItem: createdAt ?? new Date(),
          taxes: taxes?.map(({ id, itemId, serviceId, ...taxRest }) => taxRest)
        }));
        currentPaymentInvoice.push(...updatedData)
        this.f['itemInvoice'].setValue(currentPaymentInvoice)
      })
    });
  }
  handleSendInvoice() {
    if (this.invoiceForm.invalid) {
      this.markAllControlsAsTouched();
      this._toastService.showWarning("Please fill in all the invoice information completely.", " ")
      return
    }


    const dialogRef = this.sendEstimateDialog.open(this.getInvoicePayload);
    dialogRef.then((c) => {
      c.afterClosed().subscribe(res => {
        if (!res) return
        this.dataService.triggerRefreshInvoice()
        this.dialogRef.close()
      })
    });
  }

  handleModifyInvoiceItem(index?: number, item?: any) {
    const data = item && { ...item };
    data?.position == 0 ? data.description = this.createDescription(data) : data?.description
    const dialogRef = this.modifyInvoiceItemDialog.open(data);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) return
        const currentPaymentInvoice = this.f['itemInvoice'].value ?? []
        if (index === undefined) {
          res.projectName = this.projectName
          res.projectId = this.projectId ?? null
          currentPaymentInvoice.push(res)
          this.f['itemInvoice'].setValue(currentPaymentInvoice)

        }
        else {
          res.projectName = this.projectName
          res.projectId = this.projectId ?? null
          if (currentPaymentInvoice[index].dateSelectItem) {
            currentPaymentInvoice[index] = {
              ...res,
              ["itemName"]: currentPaymentInvoice[index]?.itemName,
              ["date"]: res?.dateSelectItem ?? new Date(),
              ["dateSelectItem"]: currentPaymentInvoice[index]["dateSelectItem"],
              ["serviceId"]: currentPaymentInvoice[index]?.serviceId,
              ["serviceName"]: currentPaymentInvoice[index]?.service?.serviceName ?? currentPaymentInvoice[index]?.serviceName ?? ""

            };
          }
          else {
            currentPaymentInvoice[index] = res
          }
          this.f['itemInvoice'].setValue(currentPaymentInvoice)
        }
        if (this._storeService.get_ApplyTaxAll()) {
          this._storeService.set_ApplyTaxAll(false)
          let temp = this.f['itemInvoice'].value
          temp.forEach((element: any) => {
            if (element.taxes.length > 0) {
              res.taxes.filter(x => x.selected == true).forEach((item: any) => {
                const exists = element.taxes.some((existingTax: any) => {
                  if (existingTax.companyTax) {
                    return existingTax?.companyTax.name === item?.name;
                  }
                  return existingTax.name === item.name;
                });
                if (!exists) {
                  element.taxes.push(item);
                }
              });
            } else {
              res.taxes.forEach((item: any) => {
                element.taxes.push(item)
              });
            }


          });

        }
      })
    });
  }

  handleSelectClient(item: IFilterDropdownOption) {
    this.f["clientId"].setValue(item.value)
    this.selectSearchClientElement.handleCloseSearchResult()
  }
  RouterSetting() {
    this.dialogRef.close();
    this.router.navigate(["/settings/business"])
  }
  async handleChangePicture(files: any) {

    const pictureFile = files?.[0]
    const { base64, fileName, type } = await getBase64AndFileName(pictureFile)

    this.base64 = base64
    this.type = type
    this.filename = fileName;
  }
  handleClose() {
    this.dialogRef.close();
  }
  handleModifyTaxes(item: any, index: number) {
    const itemInvoiceOld = structuredClone(this.f['itemInvoice'].value);
    const taxes = item.map(tax => {
      if (tax.companyTax) {
        tax.companyTax.selected = true;
        return tax.companyTax;
      }
      return tax;
    });
    const dialogRef = this.modifyTaxesDialog.open(taxes.filter(x => x.selected));

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) {
          this.f['itemInvoice'].setValue(itemInvoiceOld)
          return;
        }
        const currentPaymentInvoice = this.f['itemInvoice'].value ?? []
        itemInvoiceOld.forEach((item: any, indexOld: number) => {
          if (index != indexOld) {
            currentPaymentInvoice[indexOld].taxes = item.taxes
          }
        });


        currentPaymentInvoice[index].taxes = res.taxes
        this.f['itemInvoice'].setValue(currentPaymentInvoice)
        if (this._storeService.get_ApplyTaxAll()) {
          this._storeService.set_ApplyTaxAll(false)
          let temp = this.f['itemInvoice'].value
          const selectedTaxes = res.taxes.filter((tax: any) => tax.selected);

          temp.forEach((element: any) => {
            if (element.taxes.length > 0) {
              selectedTaxes.forEach((item: any) => {
                const exists = element.taxes.some((existingTax: any) => {
                  if (existingTax.companyTax) {
                    return existingTax?.companyTax.name === item?.name;
                  }
                  return existingTax.name === item.name;
                });
                if (!exists) {
                  element.taxes.push(item);
                }
              });
            } else {
              element.taxes.push(...selectedTaxes);
            }
          });
          this.calculateAllTax()
        }
      })
    });
  }
  CheckIsNaN(value: any) {
    if (isNaN(value)) {
      return 0
    }
    return value;

  }
  getFullName() {
    if (this.inforUser?.firstName && this.inforUser?.lastName) {
      return this.inforUser.firstName + " " + this.inforUser.lastName
    }
    else {
      return this.inforUser?.email ?? ""
    }

  }
  createDescription(data: any): string {
    const datePipe = new DatePipe('en-US');
    if (data?.position == 0 || this.data) {
      const description = data?.description;
      return description;

    }
    else {
      const projectName = data?.projectName;
      const itemName = data?.itemName;
      const description = data?.description;
      const serviceName = data?.serviceName;
      const formattedDate = datePipe.transform(data.dateSelectItem, 'MMM, d yyyy');
      const user = this.getFullName()
      const descriptionParts = [projectName, itemName, serviceName, `${user}- ${formattedDate}`, description];
      const descriptionnew = descriptionParts.filter(part => part != null && part !== "").join('\n');
      return descriptionnew;

    }

  }
  GetImg(fileName: string) {

    this.cdnService.GetFile(fileName).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        const reader = new FileReader();
        reader.onload = () => {
          this.imageUrl = reader.result as string;
        };
        reader.readAsDataURL(res);
      }
    }
    )

  }
  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );
    }
  }
}
