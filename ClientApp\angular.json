{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"innobook": {"projectType": "application", "schematics": {"@schematics/angular:component": {"standalone": true, "style": "scss"}, "@schematics/angular:directive": {"standalone": true}, "@schematics/angular:pipe": {"standalone": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "allowedCommonJsDependencies": ["moment-timezone"], "tsConfig": "tsconfig.app.json", "assets": ["src/assets"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "node_modules/@syncfusion/ej2-material-theme/styles/material.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.scss"], "scripts": [], "optimization": false, "sourceMap": true, "namedChunks": true, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}, "test": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "outputHashing": "none"}}, "defaultConfiguration": "development"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "innobook:build:production"}, "development": {"buildTarget": "innobook:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/assets"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/@syncfusion/ej2-material-theme/styles/material.css", "src/styles.scss"], "scripts": [], "karmaConfig": "karma.conf.js"}}}}}, "cli": {"analytics": false, "cache": {"enabled": false}}}